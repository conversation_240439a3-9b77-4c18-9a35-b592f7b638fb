"use client";

import { motion } from "framer-motion";
import { useIntersectionObserver } from "@/src/presentation/hooks/use-intersection-observer";

interface ProgressiveLoaderProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  delay?: number;
  className?: string;
}

export function ProgressiveLoader({ 
  children, 
  fallback, 
  delay = 0,
  className = ""
}: ProgressiveLoaderProps) {
  const { ref, isIntersecting } = useIntersectionObserver({
    threshold: 0.1,
    rootMargin: "100px",
  });

  return (
    <div ref={ref} className={className}>
      {isIntersecting ? (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ 
            duration: 0.6, 
            delay,
            ease: [0.25, 0.46, 0.45, 0.94]
          }}
        >
          {children}
        </motion.div>
      ) : (
        fallback || <div className="h-96" />
      )}
    </div>
  );
}
