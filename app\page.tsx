import { Navigation } from "@/src/presentation/components/landing/navigation";
import { HeroSection } from "@/src/presentation/components/landing/hero-section";
import {
  LazyFeaturesSection,
  LazyBenefitsSection,
  LazyCTASection,
  LazyFooter
} from "@/src/presentation/components/landing/lazy-sections";
import { ProgressiveLoader } from "@/src/presentation/components/ui/progressive-loader";
import { Skeleton } from "@/src/presentation/components/ui/skeleton";

export default function Home() {
  return (
    <div className="min-h-screen">
      <Navigation />
      <HeroSection />

      <ProgressiveLoader
        fallback={
          <section className="py-24 px-4 sm:px-6 lg:px-8">
            <div className="max-w-7xl mx-auto">
              <div className="text-center mb-16">
                <Skeleton className="h-12 w-96 mx-auto mb-4" />
                <Skeleton className="h-6 w-[600px] mx-auto" />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {Array.from({ length: 6 }).map((_, i) => (
                  <Skeleton key={i} className="h-48 w-full rounded-xl" />
                ))}
              </div>
            </div>
          </section>
        }
      >
        <LazyFeaturesSection />
      </ProgressiveLoader>

      <ProgressiveLoader
        delay={0.1}
        fallback={
          <section className="py-24 px-4 sm:px-6 lg:px-8 bg-muted/30">
            <div className="max-w-7xl mx-auto">
              <Skeleton className="h-96 w-full rounded-xl" />
            </div>
          </section>
        }
      >
        <LazyBenefitsSection />
      </ProgressiveLoader>

      <ProgressiveLoader
        delay={0.2}
        fallback={<Skeleton className="h-64 w-full" />}
      >
        <LazyCTASection />
      </ProgressiveLoader>

      <ProgressiveLoader
        delay={0.3}
        fallback={<Skeleton className="h-32 w-full" />}
      >
        <LazyFooter />
      </ProgressiveLoader>
    </div>
  );
}
