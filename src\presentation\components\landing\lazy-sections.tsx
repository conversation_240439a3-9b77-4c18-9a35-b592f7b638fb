"use client";

import dynamic from "next/dynamic";
import { Skeleton } from "@/src/presentation/components/ui/skeleton";

// Skeleton components for loading states
function FeaturesSkeleton() {
  return (
    <section className="py-24 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <Skeleton className="h-12 w-96 mx-auto mb-4" />
          <Skeleton className="h-6 w-[600px] mx-auto" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="space-y-4">
              <Skeleton className="h-48 w-full rounded-xl" />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

function BenefitsSkeleton() {
  return (
    <section className="py-24 px-4 sm:px-6 lg:px-8 bg-muted/30">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <Skeleton className="h-12 w-96 mx-auto mb-4" />
          <Skeleton className="h-6 w-[600px] mx-auto" />
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {Array.from({ length: 3 }).map((_, i) => (
            <Skeleton key={i} className="h-96 w-full rounded-xl" />
          ))}
        </div>
      </div>
    </section>
  );
}

function CTASkeleton() {
  return (
    <section className="py-24 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto text-center">
        <Skeleton className="h-8 w-48 mx-auto mb-8" />
        <Skeleton className="h-16 w-[600px] mx-auto mb-6" />
        <Skeleton className="h-6 w-[500px] mx-auto mb-12" />
        <div className="flex justify-center gap-4">
          <Skeleton className="h-12 w-32" />
          <Skeleton className="h-12 w-32" />
        </div>
      </div>
    </section>
  );
}

function FooterSkeleton() {
  return (
    <footer className="bg-card/50 backdrop-blur-sm border-t border-border/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <Skeleton className="h-32 w-full" />
      </div>
    </footer>
  );
}

// Lazy-loaded components
export const LazyFeaturesSection = dynamic(
  () => import("./features-section").then((mod) => ({ default: mod.FeaturesSection })),
  {
    loading: () => <FeaturesSkeleton />,
    ssr: false,
  }
);

export const LazyBenefitsSection = dynamic(
  () => import("./benefits-section").then((mod) => ({ default: mod.BenefitsSection })),
  {
    loading: () => <BenefitsSkeleton />,
    ssr: false,
  }
);

export const LazyCTASection = dynamic(
  () => import("./cta-section").then((mod) => ({ default: mod.CTASection })),
  {
    loading: () => <CTASkeleton />,
    ssr: false,
  }
);

export const LazyFooter = dynamic(
  () => import("./footer").then((mod) => ({ default: mod.Footer })),
  {
    loading: () => <FooterSkeleton />,
    ssr: false,
  }
);
